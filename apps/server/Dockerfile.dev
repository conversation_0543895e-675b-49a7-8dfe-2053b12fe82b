# Development Dockerfile for NestJS Server
FROM node:20-alpine

# Install system dependencies
RUN apk add --no-cache libc6-compat

# Set working directory
WORKDIR /app

# Copy package files for dependency installation
COPY package.json package-lock.json* ./
COPY apps/server/package.json ./apps/server/
COPY packages/eslint-config/package.json ./packages/eslint-config/
COPY packages/typescript-config/package.json ./packages/typescript-config/

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Set working directory to server app
WORKDIR /app/apps/server

# Expose ports
EXPOSE 3000 9229

# Set environment
ENV NODE_ENV=development
ENV PORT=3000

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nestjs
USER nestjs

# Start development server with hot reload
CMD ["npm", "run", "start:dev"]
