# 🚀 Deployment Guide

## 🎯 Recommended: Railway.app Deployment

### 📋 Prerequisites

1. GitHub repository with your monorepo
2. Railway.app account (free tier available)

### 🚀 Step-by-Step Deployment

#### 1. **Prepare Your Repository**

```bash
# Ensure your code is pushed to GitHub
git add .
git commit -m "Ready for deployment"
git push origin main
```

#### 2. **Deploy on Railway.app**

1. **Go to [Railway.app](https://railway.app)**
2. **Connect GitHub** repository
3. **Railway will detect** both apps automatically:
   - `apps/server` → API service
   - `apps/www` → Frontend service

#### 3. **Configure Environment Variables**

**For Server (API):**

```env
NODE_ENV=production
PORT=3000
DATABASE_URL=postgresql://...  # Railway provides this
```

**For Frontend:**

```env
NODE_ENV=production
VITE_API_URL=https://your-api.railway.app
```

#### 4. **Add Database**

- Click "Add Service" → "Database" → "PostgreSQL"
- Railway automatically provides `DATABASE_URL`

### 🔧 Alternative: Manual Docker Deployment

If you prefer Docker Compose deployment:

#### **DigitalOcean Droplet ($6/month)**

```bash
# 1. Create droplet with Docker
# 2. Clone your repo
git clone https://github.com/yourusername/revisa-ai.git
cd revisa-ai

# 3. Set up environment
cp .env.example .env
# Edit .env with your values

# 4. Deploy
sudo ./scripts/docker-prod.sh deploy

# 5. Set up domain (optional)
# Point your domain to droplet IP
```

### 💰 Cost Comparison

| Platform         | Monthly Cost | Pros                    | Cons               |
| ---------------- | ------------ | ----------------------- | ------------------ |
| **Railway.app**  | $5-20        | Easy, monorepo support  | Limited free tier  |
| **Render.com**   | $7-25        | Docker support, free DB | Slower cold starts |
| **DigitalOcean** | $6-12        | Full control, cheap     | Manual setup       |
| **Heroku**       | $7-25        | Easy, mature            | No Docker Compose  |

### 🎯 Recommended Flow

1. **Start with Railway.app** (easiest)
2. **Scale to DigitalOcean** when you need more control
3. **Move to AWS/GCP** for enterprise scale

### 🔄 CI/CD Setup (Optional)

Add GitHub Actions for automated deployments:

```yaml
# .github/workflows/deploy.yml
name: Deploy to Railway
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to Railway
        uses: railway/cli@v2
        with:
          command: up
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
```

### 🛠️ Production Checklist

- [ ] Environment variables configured
- [ ] Database connected
- [ ] Domain name set up
- [ ] SSL certificate (automatic on Railway)
- [ ] Monitoring set up
- [ ] Backup strategy
- [ ] Error tracking (Sentry)

### 🆘 Troubleshooting

**Build Fails:**

- Check build logs in Railway dashboard
- Ensure all dependencies in package.json
- Verify Node.js version compatibility

**App Won't Start:**

- Check environment variables
- Verify PORT configuration
- Check database connection

**Slow Performance:**

- Enable caching
- Optimize database queries
- Consider CDN for static assets
