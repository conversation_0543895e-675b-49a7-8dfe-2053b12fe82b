{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "tasks": {"dev": {"cache": false, "persistent": true}, "start:dev": {"cache": false, "persistent": true}, "build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**", "build/**", ".next/**", "!.next/cache/**"]}, "lint": {"dependsOn": ["^lint"]}, "typecheck": {"dependsOn": ["^typecheck"]}, "test": {"dependsOn": ["^build"]}, "format": {"outputs": [], "cache": false}}}