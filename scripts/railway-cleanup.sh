#!/bin/bash

# 🧹 Railway Deployment Cleanup Script
# This script removes files that are not needed for Railway deployment

echo "🧹 Railway Deployment Cleanup"
echo "=============================="

# Check if user wants to proceed
read -p "This will remove files not needed for Railway deployment. Continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Cleanup cancelled"
    exit 1
fi

echo "🗑️  Removing Railway-specific files (for separate services deployment)..."

# Remove root Railway files (only needed for single-service deployment)
if [ -f "Dockerfile.railway" ]; then
    rm Dockerfile.railway
    echo "✅ Removed Dockerfile.railway"
fi

if [ -f "railway.json" ]; then
    rm railway.json
    echo "✅ Removed root railway.json"
fi

# Remove redundant documentation (keep the comprehensive guide)
if [ -f "RAILWAY-DEPLOYMENT.md" ]; then
    rm RAILWAY-DEPLOYMENT.md
    echo "✅ Removed RAILWAY-DEPLOYMENT.md"
fi

if [ -f "RAILWAY-DUAL-DEPLOYMENT.md" ]; then
    rm RAILWAY-DUAL-DEPLOYMENT.md
    echo "✅ Removed RAILWAY-DUAL-DEPLOYMENT.md"
fi

echo ""
echo "✅ Cleanup complete!"
echo ""
echo "📋 Files kept for Railway deployment:"
echo "   ✅ apps/server/railway.json (optimized)"
echo "   ✅ apps/www/railway.json (optimized)"
echo "   ✅ apps/server/Dockerfile"
echo "   ✅ apps/www/Dockerfile"
echo "   ✅ RAILWAY-COMPLETE-GUIDE.md (comprehensive guide)"
echo ""
echo "📋 Files kept for local development:"
echo "   ✅ docker-compose.yml"
echo "   ✅ docker-compose.prod.yml"
echo "   ✅ nginx/nginx.conf"
echo "   ✅ scripts/docker-prod.sh"
echo ""
echo "🚂 Your project is now optimized for Railway deployment!"
echo "📖 See RAILWAY-COMPLETE-GUIDE.md for deployment instructions"
