#!/bin/bash

# Revisa AI - Production Docker Management Script
# This script helps manage the production Docker environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to check if .env file exists
check_env_file() {
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Creating from .env.example..."
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_warning "Please edit .env file with your production settings before deploying!"
        else
            print_error ".env.example file not found. Please create .env file manually."
            exit 1
        fi
    fi
}

# Function to build images
build_images() {
    print_status "Building production images..."
    docker compose -f docker-compose.prod.yml build --no-cache
    print_success "Production images built successfully!"
}

# Function to start services
start_services() {
    check_env_file
    print_status "Starting production services..."
    docker compose -f docker-compose.prod.yml up -d
    print_success "Production services started!"
    
    print_status "Services are available at:"
    echo "  🌐 Application: http://localhost"
    echo "  🔧 API: http://localhost/api"
    echo "  📊 Nginx Health: http://localhost:8080/nginx-health"
}

# Function to stop services
stop_services() {
    print_status "Stopping production services..."
    docker compose -f docker-compose.prod.yml down
    print_success "Production services stopped!"
}

# Function to restart services
restart_services() {
    print_status "Restarting production services..."
    docker compose -f docker-compose.prod.yml restart
    print_success "Production services restarted!"
}

# Function to view logs
view_logs() {
    local service=$1
    if [ -z "$service" ]; then
        print_status "Showing logs for all services..."
        docker compose -f docker-compose.prod.yml logs -f
    else
        print_status "Showing logs for $service..."
        docker compose -f docker-compose.prod.yml logs -f "$service"
    fi
}

# Function to show status
show_status() {
    print_status "Production services status:"
    docker compose -f docker-compose.prod.yml ps
    echo ""
    print_status "Service health checks:"
    docker compose -f docker-compose.prod.yml exec server wget -qO- http://localhost:3000 || print_warning "Server health check failed"
    docker compose -f docker-compose.prod.yml exec www wget -qO- http://localhost:3000 || print_warning "WWW health check failed"
}

# Function to deploy (build and start)
deploy() {
    print_status "Deploying production environment..."
    build_images
    start_services
    print_success "Production deployment completed!"
}

# Function to backup data
backup_data() {
    print_status "Creating backup of production data..."
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Backup volumes if they exist
    if docker volume ls | grep -q "revisa-postgres-data"; then
        docker run --rm -v revisa-postgres-data:/data -v "$(pwd)/$backup_dir":/backup alpine tar czf /backup/postgres_data.tar.gz -C /data .
        print_success "PostgreSQL data backed up to $backup_dir/postgres_data.tar.gz"
    fi
    
    if docker volume ls | grep -q "revisa-redis-data"; then
        docker run --rm -v revisa-redis-data:/data -v "$(pwd)/$backup_dir":/backup alpine tar czf /backup/redis_data.tar.gz -C /data .
        print_success "Redis data backed up to $backup_dir/redis_data.tar.gz"
    fi
}

# Function to clean up
cleanup() {
    print_status "Cleaning up production environment..."
    docker compose -f docker-compose.prod.yml down -v --remove-orphans
    docker system prune -f
    print_success "Production environment cleaned up!"
}

# Function to show help
show_help() {
    echo "Revisa AI - Production Docker Management"
    echo ""
    echo "💡 For development: Use 'npm run dev' (much faster!)"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  build     Build production images"
    echo "  start     Start production services"
    echo "  stop      Stop production services"
    echo "  restart   Restart production services"
    echo "  deploy    Build and start production services"
    echo "  logs      View logs (optional: specify service name)"
    echo "  status    Show services status and health"
    echo "  backup    Backup production data"
    echo "  cleanup   Clean up production environment"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 deploy"
    echo "  $0 logs nginx"
    echo "  $0 backup"
}

# Main script logic
main() {
    check_docker
    
    case "${1:-help}" in
        build)
            build_images
            ;;
        start)
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        deploy)
            deploy
            ;;
        logs)
            view_logs "$2"
            ;;
        status)
            show_status
            ;;
        backup)
            backup_data
            ;;
        cleanup)
            cleanup
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
