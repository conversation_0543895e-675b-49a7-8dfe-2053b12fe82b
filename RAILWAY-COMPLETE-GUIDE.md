# 🚂 Complete Railway Deployment Guide

## 🎯 Overview

This guide shows how to deploy both your NestJS server and React Router frontend as **separate Railway services** from your monorepo. This is the recommended approach for production deployments.

## 🏗️ Architecture

```
Railway Project: revisa-ai
├── 🔧 revisa-server (apps/server/)
│   ├── URL: https://revisa-server-production.up.railway.app
│   ├── Port: 3000
│   └── Health: /
└── 🌐 revisa-frontend (apps/www/)
    ├── URL: https://revisa-frontend-production.up.railway.app
    ├── Port: 3000
    └── Health: /
```

## 🚀 Step-by-Step Deployment

### Step 1: Create Railway Project

1. **Go to [Railway.app](https://railway.app)**
2. **Sign in** with GitHub
3. **New Project** → **Deploy from GitHub repo**
4. **Select** your `revisa.ai` repository
5. **Cancel** the initial deployment (Railway will try to deploy root)

### Step 2: Deploy Server Service

1. **Add Service** → **GitHub Repo** → Select your repo again
2. **Configure Service**:
   - **Service Name**: `revisa-server`
   - **Settings** → **Source** → **Root Directory**: `apps/server`
   - **Settings** → **Build** → **Builder**: `Dockerfile`
   - **Settings** → **Build** → **Dockerfile Path**: `apps/server/Dockerfile`

3. **Environment Variables**:
   ```env
   NODE_ENV=production
   PORT=3000
   ```

4. **Deploy** and wait for completion

### Step 3: Deploy Frontend Service

1. **Add Service** → **GitHub Repo** → Select your repo again
2. **Configure Service**:
   - **Service Name**: `revisa-frontend`
   - **Settings** → **Source** → **Root Directory**: `apps/www`
   - **Settings** → **Build** → **Builder**: `Dockerfile`
   - **Settings** → **Build** → **Dockerfile Path**: `apps/www/Dockerfile`

3. **Environment Variables**:
   ```env
   NODE_ENV=production
   PORT=3000
   VITE_API_URL=https://revisa-server-production.up.railway.app
   ```
   ⚠️ **Replace the server URL** with your actual server service URL

4. **Deploy** and wait for completion

### Step 4: Update CORS Configuration

After both services are deployed, update your server's CORS configuration:

1. **Copy your frontend Railway URL**
2. **Update** `apps/server/src/main.ts`:
   ```typescript
   app.enableCors({
     origin: [
       'http://localhost:5173', // Development
       'https://revisa-frontend-production.up.railway.app', // Production
     ],
     credentials: true,
   });
   ```
3. **Commit and push** - Railway will auto-deploy

### Step 5: Test Deployment

1. **Server**: Visit `https://revisa-server-production.up.railway.app`
2. **Frontend**: Visit `https://revisa-frontend-production.up.railway.app`
3. **API Integration**: Test that frontend can call server APIs

## 🔧 Optimized Configuration Files

Your Railway configs are now optimized with:

✅ **Watch Paths** - Only rebuild when relevant files change  
✅ **Health Checks** - Automatic service monitoring  
✅ **CORS Support** - Frontend can communicate with backend  
✅ **Proper Start Commands** - Reliable service startup  

## 💰 Cost Estimation

**Railway Pricing** (as of 2025):
- **Hobby Plan**: $5/month per service
- **Total for both apps**: ~$10/month
- **Free tier**: $5 credit/month (covers 1 service)

## 🛠️ Development Workflow

### Local Development
```bash
# Start both apps locally
npm run dev

# Server: http://localhost:3000
# Frontend: http://localhost:5173
```

### Deployment
```bash
# Commit changes
git add .
git commit -m "Update apps"
git push origin main

# Railway auto-deploys both services
```

## 🔍 Troubleshooting

### Build Failures
- **Check logs** in Railway dashboard
- **Verify Dockerfile paths** are correct
- **Ensure dependencies** are in package.json

### CORS Issues
- **Add frontend URL** to server CORS config
- **Check environment variables** are set correctly

### Slow Builds
- **Watch paths configured** to prevent unnecessary rebuilds
- **Docker builder** is faster than Nixpacks for monorepos

## 🎯 Next Steps

1. **Custom Domains**: Set up custom domains for both services
2. **Database**: Add PostgreSQL service if needed
3. **Monitoring**: Set up error tracking and monitoring
4. **CI/CD**: Add GitHub Actions for automated testing

## 📊 Benefits of This Setup

✅ **Independent scaling** - Scale server and frontend separately  
✅ **Independent deployments** - Deploy apps independently  
✅ **Cost efficient** - Only pay for what you use  
✅ **Easy rollbacks** - Rollback services independently  
✅ **Clear separation** - Clean architecture boundaries  
✅ **Optimized builds** - Watch paths prevent unnecessary rebuilds  

Your monorepo is now Railway-ready with optimized configurations! 🚀
