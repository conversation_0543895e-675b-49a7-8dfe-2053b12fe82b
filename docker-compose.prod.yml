services:
  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: revisa-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    networks:
      - revisa-network
    restart: unless-stopped
    depends_on:
      - server
      - www

  # NestJS Server API
  server:
    build:
      context: .
      dockerfile: apps/server/Dockerfile
    image: revisa-server:latest
    container_name: revisa-server
    expose:
      - "3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
    env_file:
      - .env
    networks:
      - revisa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # React Router Frontend
  www:
    build:
      context: .
      dockerfile: apps/www/Dockerfile
    image: revisa-www:latest
    container_name: revisa-www
    expose:
      - "3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
    env_file:
      - .env
    networks:
      - revisa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      server:
        condition: service_healthy
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # PostgreSQL Database (Future Use)
  # postgres:
  #   image: postgres:15-alpine
  #   container_name: revisa-postgres
  #   environment:
  #     POSTGRES_DB: ${DB_NAME:-revisa_ai}
  #     POSTGRES_USER: ${DB_USERNAME:-revisa_user}
  #     POSTGRES_PASSWORD: ${DB_PASSWORD:-secure_password}
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #     - ./database/init:/docker-entrypoint-initdb.d:ro
  #   networks:
  #     - revisa-network
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD-SHELL", "pg_isready -U ${DB_USERNAME:-revisa_user}"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3

  # Redis Cache (Future Use)
  # redis:
  #   image: redis:7-alpine
  #   container_name: revisa-redis
  #   command: redis-server --requirepass ${REDIS_PASSWORD:-secure_redis_password}
  #   volumes:
  #     - redis_data:/data
  #   networks:
  #     - revisa-network
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD", "redis-cli", "ping"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3

networks:
  revisa-network:
    driver: bridge
    name: revisa-network

volumes:
  postgres_data:
    name: revisa-postgres-data
  redis_data:
    name: revisa-redis-data
