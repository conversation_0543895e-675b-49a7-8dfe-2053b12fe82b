{"name": "www", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc", "lint": "eslint ."}, "dependencies": {"@react-router/node": "^7.5.3", "@react-router/serve": "^7.5.3", "isbot": "^5.1.27", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.5.3"}, "devDependencies": {"@react-router/dev": "^7.5.3", "@revisa/eslint-config": "*", "@revisa/typescript-config": "*", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "prettier": "^3.4.2", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}