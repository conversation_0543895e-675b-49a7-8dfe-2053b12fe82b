{"$schema": "https://json.schemastore.org/tsconfig", "display": "React", "extends": "./base.json", "compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "types": ["vite/client"]}, "include": ["app", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}