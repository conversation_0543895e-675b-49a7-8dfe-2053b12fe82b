{"$schema": "https://json.schemastore.org/tsconfig", "display": "NestJS", "extends": "./base.json", "compilerOptions": {"target": "ES2021", "module": "CommonJS", "lib": ["ES2021"], "moduleResolution": "node", "outDir": "./dist", "rootDir": "./src", "baseUrl": "./", "incremental": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test"]}