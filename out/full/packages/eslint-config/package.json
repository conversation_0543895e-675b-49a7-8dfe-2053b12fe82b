{"name": "@revisa/eslint-config", "version": "0.0.0", "type": "module", "private": true, "exports": {"./base": "./base.js", "./nestjs": "./nestjs.js", "./react": "./react.js"}, "devDependencies": {"@eslint/js": "^9.30.0", "eslint": "^9.30.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-turbo": "^2.5.0", "globals": "^16.2.0", "typescript": "^5.8.2", "typescript-eslint": "^8.35.0"}}