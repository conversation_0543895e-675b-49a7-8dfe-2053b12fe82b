{"$schema": "https://turborepo.com/schema.json", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**", "build/**", ".next/**", "!.next/cache/**"]}, "dev": {"cache": false, "persistent": true}, "format": {"cache": false, "outputs": []}, "lint": {"dependsOn": ["^lint"]}, "start:dev": {"cache": false, "persistent": true}, "test": {"dependsOn": ["^build"]}, "typecheck": {"dependsOn": ["^typecheck"]}}, "ui": "tui"}