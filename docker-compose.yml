services:
  # NestJS Server API
  server:
    build:
      context: .
      dockerfile: apps/server/Dockerfile
    image: revisa-server:latest
    container_name: revisa-server
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
    networks:
      - revisa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # React Router Frontend
  www:
    build:
      context: .
      dockerfile: apps/www/Dockerfile
    image: revisa-www:latest
    container_name: revisa-www
    ports:
      - "3002:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
    networks:
      - revisa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      server:
        condition: service_healthy

networks:
  revisa-network:
    driver: bridge
    name: revisa-network

# Optional: Add volumes for persistent data
volumes:
  server_data:
    name: revisa-server-data
  www_data:
    name: revisa-www-data
