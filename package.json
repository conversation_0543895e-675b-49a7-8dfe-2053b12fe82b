{"name": "revisa-ai", "private": true, "packageManager": "npm@10.9.2", "workspaces": ["apps/*", "packages/*"], "devDependencies": {"prettier": "^3.4.2", "turbo": "^2.5.4", "typescript": "^5.8.2"}, "scripts": {"dev": "turbo run dev start:dev --parallel", "dev:server": "cd apps/server && npm run start:dev", "dev:www": "cd apps/www && npm run dev", "build": "turbo run build", "start": "cd apps/server && node dist/main.js", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,md,json}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,md,json}\"", "test": "turbo run test", "typecheck": "turbo run typecheck"}}